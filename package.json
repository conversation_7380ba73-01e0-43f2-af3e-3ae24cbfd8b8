{"name": "purgion", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@radix-ui/react-alert-dialog": "^1.1.14", "@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-collapsible": "^1.1.11", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-tooltip": "^1.2.7", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "framer-motion": "^12.18.1", "lucide-react": "^0.515.0", "next": "15.3.3", "next-themes": "^0.4.6", "react": "19.1.0", "react-dom": "19.1.0", "recharts": "^2.15.3", "tailwind-merge": "^3.3.1"}, "devDependencies": {"@eslint/eslintrc": "^3.3.1", "@tailwindcss/postcss": "^4.1.10", "@types/node": "^22.15.31", "@types/react": "^19.0.10", "@types/react-dom": "^19.1.6", "eslint": "^9.29.0", "eslint-config-next": "15.3.3", "tailwindcss": "^4.1.10", "tw-animate-css": "^1.3.4", "typescript": "^5.8.3"}, "overrides": {"@types/react": "19.1.8", "@types/react-dom": "19.1.6"}}