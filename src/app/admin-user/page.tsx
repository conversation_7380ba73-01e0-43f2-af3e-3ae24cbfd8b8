"use client";

import { useState } from "react";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  ChevronDown,
  ChevronUp,
  Search,
  UserPlus,
  Mail,
  Calendar,
  Shield,
  User,
  Users,
  ChevronLeft,
  ChevronRight,
  ChevronsLeft,
  ChevronsRight,
} from "lucide-react";
import { Input } from "@/components/ui/input";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Label } from "@/components/ui/label";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { MoreHorizontal, Eye, PenSquare, Trash2, Plus } from "lucide-react";
import { motion } from "framer-motion";

interface User {
  _id: string;
  name: string;
  email: string;
  role: "admin" | "user";
  createdAt: string;
  updatedAt: string;
}

// Dummy data
const dummyUsers: User[] = [
  {
    _id: "1",
    name: "John Doe",
    email: "<EMAIL>",
    role: "admin",
    createdAt: "2024-01-15T10:30:00Z",
    updatedAt: "2024-01-15T10:30:00Z",
  },
  {
    _id: "2",
    name: "Jane Smith",
    email: "<EMAIL>",
    role: "user",
    createdAt: "2024-02-20T14:45:00Z",
    updatedAt: "2024-02-20T14:45:00Z",
  },
  {
    _id: "3",
    name: "Mike Johnson",
    email: "<EMAIL>",
    role: "user",
    createdAt: "2024-03-10T09:15:00Z",
    updatedAt: "2024-03-10T09:15:00Z",
  },
  {
    _id: "4",
    name: "Sarah Wilson",
    email: "<EMAIL>",
    role: "admin",
    createdAt: "2024-01-05T16:20:00Z",
    updatedAt: "2024-01-05T16:20:00Z",
  },
  {
    _id: "5",
    name: "David Brown",
    email: "<EMAIL>",
    role: "user",
    createdAt: "2024-03-25T11:00:00Z",
    updatedAt: "2024-03-25T11:00:00Z",
  },
];

const formatDate = (dateString: string) => {
  return new Date(dateString).toLocaleDateString("en-US", {
    year: "numeric",
    month: "short",
    day: "numeric",
  });
};

export default function UsersPage() {
  const [searchTerm, setSearchTerm] = useState("");
  const [sortBy, setSortBy] = useState<keyof User | null>(null);
  const [sortOrder, setSortOrder] = useState<"asc" | "desc">("asc");
  const [currentPage, setCurrentPage] = useState(1);
  const [isCreateCampaignOpen, setIsCreateCampaignOpen] = useState(false);
  const [selectedUser, setSelectedUser] = useState<User | null>(null);
  const itemsPerPage = 10;

  // Filter and sort data
  const filteredUsers = dummyUsers.filter(
    (user) =>
      user.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      user.email.toLowerCase().includes(searchTerm.toLowerCase()) ||
      user.role.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const sortedUsers = [...filteredUsers].sort((a, b) => {
    if (!sortBy) return 0;

    const aValue = a[sortBy];
    const bValue = b[sortBy];

    if (sortOrder === "asc") {
      return aValue < bValue ? -1 : aValue > bValue ? 1 : 0;
    } else {
      return aValue > bValue ? -1 : aValue < bValue ? 1 : 0;
    }
  });

  // Pagination
  const totalPages = Math.ceil(sortedUsers.length / itemsPerPage);
  const startIndex = (currentPage - 1) * itemsPerPage;
  const paginatedUsers = sortedUsers.slice(
    startIndex,
    startIndex + itemsPerPage
  );

  const handleSort = (column: keyof User) => {
    if (sortBy === column) {
      setSortOrder(sortOrder === "asc" ? "desc" : "asc");
    } else {
      setSortBy(column);
      setSortOrder("asc");
    }
  };

  const SortableHeader = ({
    column,
    children,
  }: {
    column: keyof User;
    children: React.ReactNode;
  }) => (
    <Button
      variant="ghost"
      onClick={() => handleSort(column)}
      className="h-8 px-2 text-left font-medium"
    >
      {children}
      {sortBy === column &&
        (sortOrder === "asc" ? (
          <ChevronUp className="ml-2 h-4 w-4" />
        ) : (
          <ChevronDown className="ml-2 h-4 w-4" />
        ))}
    </Button>
  );

  return (
    <div className="min-h-screen bg-secondary text-foreground py-10">
      <div className="max-w-7xl mx-auto px-4 space-y-12">
        {/* Header Section */}
        <motion.div
          className="flex items-center justify-between"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
        >
          <div>
            <h1 className="text-3xl md:text-4xl font-semibold tracking-tighter bg-gradient-to-b from-foreground to-muted-foreground/70 bg-clip-text text-transparent">
              User Management
            </h1>
            <p className="text-muted-foreground mt-2">
              Manage and view all registered users across the platform
            </p>
          </div>
          <motion.div whileHover={{ scale: 1.02 }} whileTap={{ scale: 0.98 }}>
            <Button className="shadow-[0_4px_16px_#3D51FFA8] hover:shadow-[0_4px_24px_#3D51FFDB] bg-primary hover:bg-primary/90 text-primary-foreground">
              <UserPlus className="mr-2 h-4 w-4" />
              Add User
            </Button>
          </motion.div>
        </motion.div>

        {/* Stats Cards */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, delay: 0.1 }}
          className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6"
        >
          {/* Total Users Card */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.1 }}
            className="relative group"
          >
            <Card className="border border-border/60 dark:border-border/40 overflow-hidden relative bg-gradient-to-t from-blue-500/10 to-card [box-shadow:0_-20px_80px_-20px_#2664ff2f_inset]">
              <div className="absolute bg-blue-600/80 size-20 blur-3xl -top-6 group-hover:saturate-150 group-hover:scale-125 transition-all duration-500"></div>
              <CardContent className="p-6 py-8 relative z-10">
                <div className="flex items-center space-x-4">
                  <div className="p-3 bg-primary/20 rounded-lg brightness-125">
                    <User className="size-6 text-primary" />
                  </div>
                  <div>
                    <p className="text-xl font-medium text-foreground/70 tracking-tighter">
                      Total Users
                    </p>
                    <p className="text-2xl font-bold">{dummyUsers.length}</p>
                  </div>
                </div>
              </CardContent>
            </Card>
          </motion.div>

          {/* Admin Users Card */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.2 }}
            className="relative group"
          >
            <Card className="border border-border/60 dark:border-border/40 overflow-hidden relative bg-gradient-to-t from-emerald-500/10 to-card [box-shadow:0_-20px_80px_-20px_#10b9812f_inset]">
              <div className="absolute bg-emerald-600/80 size-20 blur-3xl -top-6 group-hover:saturate-150 group-hover:scale-125 transition-all duration-500"></div>
              <CardContent className="p-6 py-8 relative z-10">
                <div className="flex items-center space-x-4">
                  <div className="p-3 bg-emerald-400/20 rounded-lg brightness-125">
                    <Shield className="size-6 text-emerald-400" />
                  </div>
                  <div>
                    <p className="text-xl font-medium text-foreground/70 tracking-tighter">
                      Admin Users
                    </p>
                    <p className="text-2xl font-bold">
                      {dummyUsers.filter((u) => u.role === "admin").length}
                    </p>
                  </div>
                </div>
              </CardContent>
            </Card>
          </motion.div>

          {/* Regular Users Card */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.3 }}
            className="relative group"
          >
            <Card className="border border-border/60 dark:border-border/40 overflow-hidden relative bg-gradient-to-t from-purple-500/10 to-card [box-shadow:0_-20px_80px_-20px_#a855f72f_inset]">
              <div className="absolute bg-purple-600/80 size-20 blur-3xl -top-6 group-hover:saturate-150 group-hover:scale-125 transition-all duration-500"></div>
              <CardContent className="p-6 py-8 relative z-10">
                <div className="flex items-center space-x-4">
                  <div className="p-3 bg-purple-400/20 rounded-lg brightness-125">
                    <Users className="size-6 text-purple-400" />
                  </div>
                  <div>
                    <p className="text-xl font-medium text-foreground/70 tracking-tighter">
                      Regular Users
                    </p>
                    <p className="text-2xl font-bold">
                      {dummyUsers.filter((u) => u.role === "user").length}
                    </p>
                  </div>
                </div>
              </CardContent>
            </Card>
          </motion.div>

          {/* Recent Signups Card */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.4 }}
            className="relative group"
          >
            <Card className="border border-border/60 dark:border-border/40 overflow-hidden relative bg-gradient-to-t from-rose-500/10 to-card [box-shadow:0_-20px_80px_-20px_#f43f5e2f_inset]">
              <div className="absolute bg-rose-600/80 size-20 blur-3xl -top-6 group-hover:saturate-150 group-hover:scale-125 transition-all duration-500"></div>
              <CardContent className="p-6 py-8 relative z-10">
                <div className="flex items-center space-x-4">
                  <div className="p-3 bg-rose-400/20 rounded-lg brightness-125">
                    <Calendar className="size-6 text-rose-400" />
                  </div>
                  <div>
                    <p className="text-xl font-medium text-foreground/70 tracking-tighter">
                      This Month
                    </p>
                    <p className="text-2xl font-bold">3</p>
                  </div>
                </div>
              </CardContent>
            </Card>
          </motion.div>
        </motion.div>

        {/* Main Users Table */}
        <motion.div
          initial={{ opacity: 0, y: 20, scale: 0.98 }}
          animate={{ opacity: 1, y: 0, scale: 1 }}
          transition={{ duration: 0.5, delay: 0.5 }}
        >
          <Card
            className="border border-border/60 dark:border-border/40 overflow-hidden relative bg-card/90 backdrop-blur-sm [box-shadow:0_-40px_80px_-40px_#2664ff2f_inset]"
            style={{
              boxShadow: "rgba(0, 0, 0, 0.06) 0px 20px 24px",
            }}
          >
            <div className="absolute bg-primary/10 size-40 blur-3xl -top-20 -right-20 opacity-70"></div>
            <CardHeader className="pb-4 relative z-10">
              <div className="flex items-center justify-between">
                <div>
                  <CardTitle className="text-xl font-semibold tracking-tight bg-gradient-to-r from-foreground to-muted-foreground bg-clip-text text-transparent">
                    All Users
                  </CardTitle>
                  <CardDescription className="text-muted-foreground mt-1">
                    {filteredUsers.length} of {dummyUsers.length} user(s) •
                    Manage user accounts and permissions
                  </CardDescription>
                </div>
                <div className="flex items-center space-x-2">
                  <div className="relative">
                    <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4" />
                    <Input
                      placeholder="Search users..."
                      value={searchTerm}
                      onChange={(event) => setSearchTerm(event.target.value)}
                      className="pl-10 w-64 border-border/60 dark:border-border/40 bg-background/50 backdrop-blur-sm focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:bg-background"
                    />
                  </div>
                </div>
              </div>
            </CardHeader>
            <CardContent className="p-6 relative z-10">
              <div className="border border-border/60 dark:border-border/40 rounded-lg overflow-hidden bg-background/30 backdrop-blur-sm">
                <Table>
                  <TableHeader>
                    <TableRow className="border-b border-border/60 dark:border-border/40 bg-muted/30">
                      <TableHead className="h-12 font-semibold">
                        <SortableHeader column="name">
                          <User className="mr-2 h-4 w-4" />
                          Name
                        </SortableHeader>
                      </TableHead>
                      <TableHead className="h-12 font-semibold">
                        <SortableHeader column="email">
                          <Mail className="mr-2 h-4 w-4" />
                          Email
                        </SortableHeader>
                      </TableHead>
                      <TableHead className="h-12 font-semibold">
                        <SortableHeader column="role">
                          <Shield className="mr-2 h-4 w-4" />
                          Role
                        </SortableHeader>
                      </TableHead>
                      <TableHead className="h-12 font-semibold">
                        <SortableHeader column="createdAt">
                          <Calendar className="mr-2 h-4 w-4" />
                          Joined
                        </SortableHeader>
                      </TableHead>
                      <TableHead className="h-12 font-semibold">
                        Actions
                      </TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {paginatedUsers.length > 0 ? (
                      paginatedUsers.map((user, index) => (
                        <motion.tr
                          key={user._id}
                          initial={{ opacity: 0, y: 20 }}
                          animate={{ opacity: 1, y: 0 }}
                          transition={{ duration: 0.3, delay: index * 0.1 }}
                          className="border-b border-border/40 hover:bg-accent/30 dark:hover:bg-accent/50 transition-all duration-200 group"
                        >
                          <TableCell className="py-4">
                            <div className="flex items-center space-x-3">
                              <div className="relative">
                                <div className="flex h-10 w-10 items-center justify-center rounded-full bg-gradient-to-br from-primary/20 to-primary/10 border border-primary/20 group-hover:scale-110 transition-transform duration-200">
                                  <User className="h-5 w-5 text-primary" />
                                </div>
                                <div className="absolute -inset-1 bg-primary/20 rounded-full blur-sm opacity-0 group-hover:opacity-100 transition-opacity duration-200"></div>
                              </div>
                              <div>
                                <div className="font-semibold text-foreground group-hover:text-primary transition-colors duration-200">
                                  {user.name}
                                </div>
                              </div>
                            </div>
                          </TableCell>
                          <TableCell className="py-4">
                            <div className="text-muted-foreground group-hover:text-foreground transition-colors duration-200">
                              {user.email}
                            </div>
                          </TableCell>
                          <TableCell className="py-4">
                            <Badge
                              variant={
                                user.role === "admin" ? "default" : "secondary"
                              }
                              className={`capitalize font-medium ${
                                user.role === "admin"
                                  ? "bg-emerald-500/20 text-emerald-400 border-emerald-500/30 hover:bg-emerald-500/30"
                                  : "bg-blue-500/20 text-blue-400 border-blue-500/30 hover:bg-blue-500/30"
                              } transition-colors duration-200`}
                            >
                              {user.role}
                            </Badge>
                          </TableCell>
                          <TableCell className="py-4">
                            <div className="text-muted-foreground group-hover:text-foreground transition-colors duration-200">
                              {formatDate(user.createdAt)}
                            </div>
                          </TableCell>
                          <TableCell className="py-4">
                            <DropdownMenu>
                              <DropdownMenuTrigger asChild>
                                <Button
                                  variant="ghost"
                                  className="h-8 w-8 p-0 hover:bg-accent/50 group-hover:bg-primary/10 transition-colors duration-200"
                                >
                                  <span className="sr-only">Open menu</span>
                                  <MoreHorizontal className="h-4 w-4 group-hover:text-primary transition-colors duration-200" />
                                </Button>
                              </DropdownMenuTrigger>
                              <DropdownMenuContent
                                align="end"
                                className="w-48 border-border/60 dark:border-border/40 bg-card/90 backdrop-blur-sm"
                              >
                                <DropdownMenuItem className="hover:bg-accent/50 cursor-pointer transition-colors duration-200">
                                  <Eye className="mr-2 h-4 w-4" />
                                  View details
                                </DropdownMenuItem>
                                <DropdownMenuItem className="hover:bg-accent/50 cursor-pointer transition-colors duration-200">
                                  <PenSquare className="mr-2 h-4 w-4" />
                                  Edit user
                                </DropdownMenuItem>
                                {user.role === "user" && (
                                  <DropdownMenuItem
                                    onClick={() => {
                                      setSelectedUser(user);
                                      setIsCreateCampaignOpen(true);
                                    }}
                                    className="text-emerald-400 hover:bg-emerald-500/10 cursor-pointer transition-colors duration-200"
                                  >
                                    <Plus className="mr-2 h-4 w-4" />
                                    Create Campaign
                                  </DropdownMenuItem>
                                )}
                                <DropdownMenuItem className="text-destructive hover:bg-destructive/10 cursor-pointer transition-colors duration-200">
                                  <Trash2 className="mr-2 h-4 w-4" />
                                  Delete user
                                </DropdownMenuItem>
                              </DropdownMenuContent>
                            </DropdownMenu>
                          </TableCell>
                        </motion.tr>
                      ))
                    ) : (
                      <TableRow>
                        <TableCell
                          colSpan={5}
                          className="h-24 text-center text-muted-foreground"
                        >
                          <div className="flex flex-col items-center space-y-2">
                            <User className="h-8 w-8 text-muted-foreground/50" />
                            <p>No users found.</p>
                          </div>
                        </TableCell>
                      </TableRow>
                    )}
                  </TableBody>
                </Table>
              </div>

              {/* Pagination */}
              <div className="flex items-center justify-between px-6 py-4 border-t border-border/60 dark:border-border/40 bg-muted/20">
                <div className="flex items-center space-x-2">
                  <p className="text-sm text-muted-foreground">
                    Showing{" "}
                    <span className="font-medium text-foreground">
                      {startIndex + 1}
                    </span>{" "}
                    to{" "}
                    <span className="font-medium text-foreground">
                      {Math.min(startIndex + itemsPerPage, sortedUsers.length)}
                    </span>{" "}
                    of{" "}
                    <span className="font-medium text-foreground">
                      {sortedUsers.length}
                    </span>{" "}
                    results
                  </p>
                </div>
                <div className="flex items-center space-x-2">
                  <motion.div
                    whileHover={{ scale: 1.05 }}
                    whileTap={{ scale: 0.95 }}
                  >
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => setCurrentPage(1)}
                      disabled={currentPage === 1}
                      className="border-border/60 dark:border-border/40 hover:bg-primary/10 hover:border-primary/50 transition-colors duration-200"
                    >
                      <ChevronsLeft className="h-4 w-4" />
                    </Button>
                  </motion.div>
                  <motion.div
                    whileHover={{ scale: 1.05 }}
                    whileTap={{ scale: 0.95 }}
                  >
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => setCurrentPage(currentPage - 1)}
                      disabled={currentPage === 1}
                      className="border-border/60 dark:border-border/40 hover:bg-primary/10 hover:border-primary/50 transition-colors duration-200"
                    >
                      <ChevronLeft className="h-4 w-4" />
                    </Button>
                  </motion.div>
                  <div className="flex items-center space-x-1 px-3 py-1 bg-primary/10 rounded-md border border-primary/20">
                    <span className="text-sm text-muted-foreground">Page</span>
                    <span className="text-sm font-medium text-primary">
                      {currentPage} of {totalPages}
                    </span>
                  </div>
                  <motion.div
                    whileHover={{ scale: 1.05 }}
                    whileTap={{ scale: 0.95 }}
                  >
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => setCurrentPage(currentPage + 1)}
                      disabled={currentPage === totalPages}
                      className="border-border/60 dark:border-border/40 hover:bg-primary/10 hover:border-primary/50 transition-colors duration-200"
                    >
                      <ChevronRight className="h-4 w-4" />
                    </Button>
                  </motion.div>
                  <motion.div
                    whileHover={{ scale: 1.05 }}
                    whileTap={{ scale: 0.95 }}
                  >
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => setCurrentPage(totalPages)}
                      disabled={currentPage === totalPages}
                      className="border-border/60 dark:border-border/40 hover:bg-primary/10 hover:border-primary/50 transition-colors duration-200"
                    >
                      <ChevronsRight className="h-4 w-4" />
                    </Button>
                  </motion.div>
                </div>
              </div>
            </CardContent>
          </Card>
        </motion.div>

        {/* Campaign Creation Dialog */}
        <Dialog
          open={isCreateCampaignOpen}
          onOpenChange={setIsCreateCampaignOpen}
        >
          <DialogContent className="sm:max-w-[500px] border-border/60 dark:border-border/40 bg-card/90 backdrop-blur-sm [box-shadow:0_-20px_80px_-20px_#2664ff2f_inset] relative overflow-hidden">
            <div className="absolute bg-primary/10 size-32 blur-3xl -top-16 -right-16 opacity-70"></div>
            <DialogHeader className="relative z-10">
              <DialogTitle className="text-foreground text-xl font-semibold">
                Create Campaign for {selectedUser?.name || "User"}
              </DialogTitle>
              <DialogDescription className="text-muted-foreground">
                Create a new campaign assignment for this user. Fill in the
                campaign details below.
              </DialogDescription>
            </DialogHeader>
            <div className="space-y-4 relative z-10">
              <div className="space-y-2">
                <Label htmlFor="title" className="text-foreground font-medium">
                  Campaign Title *
                </Label>
                <Input
                  id="title"
                  placeholder="Enter campaign title"
                  defaultValue=""
                  className="border-border/60 dark:border-border/40 bg-background/50 backdrop-blur-sm focus-visible:border-primary focus-visible:ring-primary/20"
                />
              </div>

              <div className="space-y-2">
                <Label
                  htmlFor="description"
                  className="text-foreground font-medium"
                >
                  Description
                </Label>
                <textarea
                  id="description"
                  className="flex min-h-[80px] w-full rounded-md border border-border/60 dark:border-border/40 bg-background/50 backdrop-blur-sm px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-primary/20 focus-visible:border-primary disabled:cursor-not-allowed disabled:opacity-50"
                  placeholder="Enter campaign description (optional)"
                  rows={3}
                />
              </div>

              <div className="space-y-2">
                <Label
                  htmlFor="platform"
                  className="text-foreground font-medium"
                >
                  Platform *
                </Label>
                <Select>
                  <SelectTrigger className="border-border/60 dark:border-border/40 bg-background/50 backdrop-blur-sm focus:border-primary focus:ring-primary/20">
                    <SelectValue placeholder="Select platform" />
                  </SelectTrigger>
                  <SelectContent className="border-border/60 dark:border-border/40 bg-card/90 backdrop-blur-sm">
                    <SelectItem value="instagram">Instagram</SelectItem>
                    <SelectItem value="twitter">Twitter</SelectItem>
                    <SelectItem value="facebook">Facebook</SelectItem>
                    <SelectItem value="youtube">YouTube</SelectItem>
                    <SelectItem value="tiktok">TikTok</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <Label htmlFor="url" className="text-foreground font-medium">
                  Campaign URL *
                </Label>
                <Input
                  id="url"
                  type="url"
                  placeholder="https://example.com/campaign"
                  className="border-border/60 dark:border-border/40 bg-background/50 backdrop-blur-sm focus-visible:border-primary focus-visible:ring-primary/20"
                />
              </div>

              <div className="space-y-2">
                <Label
                  htmlFor="deadline"
                  className="text-foreground font-medium"
                >
                  Deadline
                </Label>
                <Input
                  id="deadline"
                  type="datetime-local"
                  min={new Date().toISOString().slice(0, 16)}
                  className="border-border/60 dark:border-border/40 bg-background/50 backdrop-blur-sm focus-visible:border-primary focus-visible:ring-primary/20"
                />
              </div>
            </div>

            <DialogFooter className="relative z-10">
              <Button
                type="button"
                variant="outline"
                onClick={() => setIsCreateCampaignOpen(false)}
                className="border-border/60 dark:border-border/40 hover:bg-muted/50 transition-colors duration-200"
              >
                Cancel
              </Button>
              <Button
                type="button"
                className="shadow-[0_4px_16px_#3D51FFA8] hover:shadow-[0_4px_24px_#3D51FFDB] bg-primary hover:bg-primary/90 text-primary-foreground transition-all duration-200"
              >
                Create Campaign
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>
      </div>
    </div>
  );
}
