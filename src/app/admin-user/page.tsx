"use client";

import { useState } from "react";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  ChevronDown,
  ChevronUp,
  Search,
  UserPlus,
  Mail,
  Calendar,
  Shield,
  User,
  ChevronLeft,
  ChevronRight,
  ChevronsLeft,
  ChevronsRight,
} from "lucide-react";
import { Input } from "@/components/ui/input";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Label } from "@/components/ui/label";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { MoreHorizontal, Eye, PenSquare, Trash2, Plus } from "lucide-react";
import { motion } from "framer-motion";

interface User {
  _id: string;
  name: string;
  email: string;
  role: "admin" | "user";
  createdAt: string;
  updatedAt: string;
}

// Dummy data
const dummyUsers: User[] = [
  {
    _id: "1",
    name: "John Doe",
    email: "<EMAIL>",
    role: "admin",
    createdAt: "2024-01-15T10:30:00Z",
    updatedAt: "2024-01-15T10:30:00Z",
  },
  {
    _id: "2",
    name: "Jane Smith",
    email: "<EMAIL>",
    role: "user",
    createdAt: "2024-02-20T14:45:00Z",
    updatedAt: "2024-02-20T14:45:00Z",
  },
  {
    _id: "3",
    name: "Mike Johnson",
    email: "<EMAIL>",
    role: "user",
    createdAt: "2024-03-10T09:15:00Z",
    updatedAt: "2024-03-10T09:15:00Z",
  },
  {
    _id: "4",
    name: "Sarah Wilson",
    email: "<EMAIL>",
    role: "admin",
    createdAt: "2024-01-05T16:20:00Z",
    updatedAt: "2024-01-05T16:20:00Z",
  },
  {
    _id: "5",
    name: "David Brown",
    email: "<EMAIL>",
    role: "user",
    createdAt: "2024-03-25T11:00:00Z",
    updatedAt: "2024-03-25T11:00:00Z",
  },
];

const formatDate = (dateString: string) => {
  return new Date(dateString).toLocaleDateString("en-US", {
    year: "numeric",
    month: "short",
    day: "numeric",
  });
};

export default function UsersPage() {
  const [searchTerm, setSearchTerm] = useState("");
  const [sortBy, setSortBy] = useState<keyof User | null>(null);
  const [sortOrder, setSortOrder] = useState<"asc" | "desc">("asc");
  const [currentPage, setCurrentPage] = useState(1);
  const [isCreateCampaignOpen, setIsCreateCampaignOpen] = useState(false);
  const [selectedUser, setSelectedUser] = useState<User | null>(null);
  const itemsPerPage = 10;

  // Filter and sort data
  const filteredUsers = dummyUsers.filter(
    (user) =>
      user.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      user.email.toLowerCase().includes(searchTerm.toLowerCase()) ||
      user.role.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const sortedUsers = [...filteredUsers].sort((a, b) => {
    if (!sortBy) return 0;

    const aValue = a[sortBy];
    const bValue = b[sortBy];

    if (sortOrder === "asc") {
      return aValue < bValue ? -1 : aValue > bValue ? 1 : 0;
    } else {
      return aValue > bValue ? -1 : aValue < bValue ? 1 : 0;
    }
  });

  // Pagination
  const totalPages = Math.ceil(sortedUsers.length / itemsPerPage);
  const startIndex = (currentPage - 1) * itemsPerPage;
  const paginatedUsers = sortedUsers.slice(
    startIndex,
    startIndex + itemsPerPage
  );

  const handleSort = (column: keyof User) => {
    if (sortBy === column) {
      setSortOrder(sortOrder === "asc" ? "desc" : "asc");
    } else {
      setSortBy(column);
      setSortOrder("asc");
    }
  };

  const SortableHeader = ({
    column,
    children,
  }: {
    column: keyof User;
    children: React.ReactNode;
  }) => (
    <Button
      variant="ghost"
      onClick={() => handleSort(column)}
      className="h-8 px-2 text-left font-medium"
    >
      {children}
      {sortBy === column &&
        (sortOrder === "asc" ? (
          <ChevronUp className="ml-2 h-4 w-4" />
        ) : (
          <ChevronDown className="ml-2 h-4 w-4" />
        ))}
    </Button>
  );

  return (
    <div className="space-y-6">
      <motion.div 
        className="flex items-center justify-between"
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
      >
        <div>
          <h1 className="text-3xl font-bold tracking-tight bg-gradient-to-b from-foreground to-muted-foreground/70 bg-clip-text text-transparent">Users</h1>
          <p className="text-muted-foreground">
            Manage and view all registered users
          </p>
        </div>
        <motion.div whileHover={{ scale: 1.02 }} whileTap={{ scale: 0.98 }}>
          <Button className="shadow-[0_4px_16px_#3D51FFA8] hover:shadow-[0_4px_24px_#3D51FFDB]">
            <UserPlus className="mr-2 h-4 w-4" />
            Add User
          </Button>
        </motion.div>
      </motion.div>

      <motion.div
        initial={{ opacity: 0, y: 20, scale: 0.98 }}
        animate={{ opacity: 1, y: 0, scale: 1 }}
        transition={{ duration: 0.5, delay: 0.1 }}
      >
        <Card className="border border-border/60 dark:border-border/40 overflow-hidden relative">
          <div className="absolute bg-primary/10 size-40 blur-3xl -top-20 -right-20 opacity-70"></div>
          <CardHeader className="pb-4">
            <div className="flex items-center justify-between">
              <div>
                <CardTitle className="text-lg">All Users</CardTitle>
                <CardDescription>
                  {filteredUsers.length} of {dummyUsers.length} user(s)
                </CardDescription>
              </div>
              <div className="flex items-center space-x-2">
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4" />
                  <Input
                    placeholder="Search users..."
                    value={searchTerm}
                    onChange={(event) => setSearchTerm(event.target.value)}
                    className="pl-10 w-64 border-border focus-visible:border-ring focus-visible:ring-ring/50"
                  />
                </div>
              </div>
            </div>
          </CardHeader>
          <CardContent className="p-4">
            <div className="border border-border/60 dark:border-border/40 rounded-lg overflow-hidden">
              <Table>
                <TableHeader>
                  <TableRow className="border-b border-border/60 dark:border-border/40">
                    <TableHead className="h-12">
                      <SortableHeader column="name">
                        <User className="mr-2 h-4 w-4" />
                        Name
                      </SortableHeader>
                    </TableHead>
                    <TableHead className="h-12">
                      <SortableHeader column="email">
                        <Mail className="mr-2 h-4 w-4" />
                        Email
                      </SortableHeader>
                    </TableHead>
                    <TableHead className="h-12">
                      <SortableHeader column="role">
                        <Shield className="mr-2 h-4 w-4" />
                        Role
                      </SortableHeader>
                    </TableHead>
                    <TableHead className="h-12">
                      <SortableHeader column="createdAt">
                        <Calendar className="mr-2 h-4 w-4" />
                        Joined
                      </SortableHeader>
                    </TableHead>
                    <TableHead className="h-12">Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {paginatedUsers.length > 0 ? (
                    paginatedUsers.map((user) => (
                      <TableRow
                        key={user._id}
                        className="hover:bg-accent/30 dark:hover:bg-accent/50 transition-colors"
                      >
                        <TableCell className="py-4">
                          <div className="flex items-center space-x-3">
                            <div className="flex h-8 w-8 items-center justify-center rounded-full bg-secondary dark:bg-secondary">
                              <User className="h-4 w-4 text-secondary-foreground dark:text-secondary-foreground" />
                            </div>
                            <div>
                              <div className="font-medium text-foreground">
                                {user.name}
                              </div>
                            </div>
                          </div>
                        </TableCell>
                        <TableCell className="py-4">
                          <div className="text-muted-foreground">
                            {user.email}
                          </div>
                        </TableCell>
                        <TableCell className="py-4">
                          <Badge
                            variant={
                              user.role === "admin" ? "default" : "secondary"
                            }
                            className="capitalize"
                          >
                            {user.role}
                          </Badge>
                        </TableCell>
                        <TableCell className="py-4">
                          <div className="text-muted-foreground">
                            {formatDate(user.createdAt)}
                          </div>
                        </TableCell>
                        <TableCell className="py-4">
                          <DropdownMenu>
                            <DropdownMenuTrigger asChild>
                              <Button variant="ghost" className="h-8 w-8 p-0 hover:bg-accent/50">
                                <span className="sr-only">Open menu</span>
                                <MoreHorizontal className="h-4 w-4" />
                              </Button>
                            </DropdownMenuTrigger>
                            <DropdownMenuContent align="end" className="w-48 border-border/60 dark:border-border/40">
                              <DropdownMenuItem className="hover:bg-accent/50 cursor-pointer">
                                <Eye className="mr-2 h-4 w-4" />
                                View details
                              </DropdownMenuItem>
                              <DropdownMenuItem className="hover:bg-accent/50 cursor-pointer">
                                <PenSquare className="mr-2 h-4 w-4" />
                                Edit user
                              </DropdownMenuItem>
                              {user.role === "user" && (
                                <DropdownMenuItem
                                  onClick={() => {
                                    setSelectedUser(user);
                                    setIsCreateCampaignOpen(true);
                                  }}
                                  className="text-chart-4 hover:bg-accent/50 cursor-pointer"
                                >
                                  <Plus className="mr-2 h-4 w-4" />
                                  Create Campaign
                                </DropdownMenuItem>
                              )}
                              <DropdownMenuItem className="text-destructive hover:bg-accent/50 cursor-pointer">
                                <Trash2 className="mr-2 h-4 w-4" />
                                Delete user
                              </DropdownMenuItem>
                            </DropdownMenuContent>
                          </DropdownMenu>
                        </TableCell>
                      </TableRow>
                    ))
                  ) : (
                    <TableRow>
                      <TableCell colSpan={5} className="h-24 text-center text-muted-foreground">
                        No users found.
                      </TableCell>
                    </TableRow>
                  )}
                </TableBody>
              </Table>
            </div>

            {/* Pagination */}
            <div className="flex items-center justify-between px-6 py-4 border-t border-border/60 dark:border-border/40">
              <div className="flex items-center space-x-2">
                <p className="text-sm text-muted-foreground">
                  Showing <span className="font-medium">{startIndex + 1}</span> to{" "}
                  <span className="font-medium">
                    {Math.min(startIndex + itemsPerPage, sortedUsers.length)}
                  </span>{" "}
                  of <span className="font-medium">{sortedUsers.length}</span>{" "}
                  results
                </p>
              </div>
              <div className="flex items-center space-x-2">
                <motion.div whileHover={{ scale: 1.05 }} whileTap={{ scale: 0.95 }}>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => setCurrentPage(1)}
                    disabled={currentPage === 1}
                    className="border-border/60 dark:border-border/40"
                  >
                    <ChevronsLeft className="h-4 w-4" />
                  </Button>
                </motion.div>
                <motion.div whileHover={{ scale: 1.05 }} whileTap={{ scale: 0.95 }}>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => setCurrentPage(currentPage - 1)}
                    disabled={currentPage === 1}
                    className="border-border/60 dark:border-border/40"
                  >
                    <ChevronLeft className="h-4 w-4" />
                  </Button>
                </motion.div>
                <div className="flex items-center space-x-1">
                  <span className="text-sm text-muted-foreground">
                    Page
                  </span>
                  <span className="text-sm font-medium">
                    {currentPage} of {totalPages}
                  </span>
                </div>
                <motion.div whileHover={{ scale: 1.05 }} whileTap={{ scale: 0.95 }}>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => setCurrentPage(currentPage + 1)}
                    disabled={currentPage === totalPages}
                    className="border-border/60 dark:border-border/40"
                  >
                    <ChevronRight className="h-4 w-4" />
                  </Button>
                </motion.div>
                <motion.div whileHover={{ scale: 1.05 }} whileTap={{ scale: 0.95 }}>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => setCurrentPage(totalPages)}
                    disabled={currentPage === totalPages}
                    className="border-border/60 dark:border-border/40"
                  >
                    <ChevronsRight className="h-4 w-4" />
                  </Button>
                </motion.div>
              </div>
            </div>
          </CardContent>
        </Card>
      </motion.div>

      {/* Campaign Creation Dialog */}
      <Dialog
        open={isCreateCampaignOpen}
        onOpenChange={setIsCreateCampaignOpen}
      >
        <DialogContent className="sm:max-w-[500px] border-border/60 dark:border-border/40 bg-card/90 backdrop-blur-sm">
          <DialogHeader>
            <DialogTitle className="text-foreground">
              Create Campaign for {selectedUser?.name || "User"}
            </DialogTitle>
            <DialogDescription className="text-muted-foreground">
              Create a new campaign assignment for this user. Fill in the
              campaign details below.
            </DialogDescription>
          </DialogHeader>
          <div className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="title" className="text-foreground">Campaign Title *</Label>
              <Input
                id="title"
                placeholder="Enter campaign title"
                defaultValue=""
                className="border-border/60 dark:border-border/40"
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="description" className="text-foreground">Description</Label>
              <textarea
                id="description"
                className="flex min-h-[80px] w-full rounded-md border border-border/60 dark:border-border/40 bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
                placeholder="Enter campaign description (optional)"
                rows={3}
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="platform" className="text-foreground">Platform *</Label>
              <Select>
                <SelectTrigger className="border-border/60 dark:border-border/40">
                  <SelectValue placeholder="Select platform" />
                </SelectTrigger>
                <SelectContent className="border-border/60 dark:border-border/40 bg-card">
                  <SelectItem value="instagram">Instagram</SelectItem>
                  <SelectItem value="twitter">Twitter</SelectItem>
                  <SelectItem value="facebook">Facebook</SelectItem>
                  <SelectItem value="youtube">YouTube</SelectItem>
                  <SelectItem value="tiktok">TikTok</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label htmlFor="url" className="text-foreground">Campaign URL *</Label>
              <Input
                id="url"
                type="url"
                placeholder="https://example.com/campaign"
                className="border-border/60 dark:border-border/40"
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="deadline" className="text-foreground">Deadline</Label>
              <Input
                id="deadline"
                type="datetime-local"
                min={new Date().toISOString().slice(0, 16)}
                className="border-border/60 dark:border-border/40"
              />
            </div>
          </div>

          <DialogFooter>
            <Button
              type="button"
              variant="outline"
              onClick={() => setIsCreateCampaignOpen(false)}
              className="border-border/60 dark:border-border/40"
            >
              Cancel
            </Button>
            <Button 
              type="button"
              className="shadow-[0_4px_16px_#3D51FFA8] hover:shadow-[0_4px_24px_#3D51FFDB]"
            >
              Create Campaign
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}
