import type { <PERSON><PERSON><PERSON> } from "next";
import { <PERSON><PERSON><PERSON> } from "next/font/google";
import "./globals.css";
import { ThemeProvider } from "next-themes";
import ThemeTogglebutton from "@/components/ui/ThemeToggle";

const font = Geist({ subsets: ["latin"] });

export const metadata: Metadata = {
  title: "Nextjs + Shadcn",
  description: "Generated by create next app",
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en" suppressHydrationWarning>
      <body className={font.className}>
        <ThemeProvider attribute="class" defaultTheme="dark">
          <ThemeTogglebutton className="absolute top-2 right-6 z-30" />
          {children}
        </ThemeProvider>
      </body>
    </html>
  );
}
