"use client";

import { useState } from "react";
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import {
  ChartContainer,
  ChartTooltip,
  ChartTooltipContent,
} from "@/components/ui/chart";
import { AreaChart, Area, XAxis, YAxis, ResponsiveContainer } from "recharts";
import { Video, Heart, MessageCircle, Eye } from "lucide-react";
import Image from "next/image";
import { motion } from "framer-motion";

const chartData = [
  { week: "Week 1", likes: 1800, comments: 3202, views: 3500 },
  { week: "Week 2", likes: 1500, comments: 2800, views: 2900 },
  { week: "Week 3", likes: 4200, comments: 9800, views: 4100 },
  { week: "Week 4", likes: 3800, comments: 4200, views: 4800 },
  { week: "Week 5", likes: 4600, comments: 5100, views: 4900 },
  { week: "Week 6", likes: 4200, comments: 4800, views: 5200 },
  { week: "Week 7", likes: 4800, comments: 4600, views: 5100 },
];

export default function Dashboard() {
  const [activeTab, setActiveTab] = useState("Week");
  const [activeFilter, setActiveFilter] = useState("All");

  return (
    <div className="min-h-screen bg-secondary pb-28 text-foreground py-10">
      <div className="space-y-12">
        {/* Header */}
        <div
          className="bg-card/90 max-w-7xl rounded-2xl mx-auto p-4 md:p-6 relative overflow-hidden"
          style={{
            boxShadow: "rgba(0, 0, 0, 0.04) 0px 8px 20px",
          }}
        >
          <div className="space-y-2 mx-auto px-2 mt-4">
            <h2 className="text-3xl md:text-4xl font-semibold tracking-tighter bg-gradient-to-b from-foreground to-muted-foreground/70 bg-clip-text text-transparent">
              Summer Campaign 2024
            </h2>
            <p className="text-muted-foreground">
              Admin: John Doe • Oct 1 - Dec 31, 2024
            </p>
          </div>

          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6 md:gap-8 max-w-7xl mx-auto mt-8 mb-2 px-2">
            <motion.div
              initial={{
                opacity: 0,
                y: 20,
                scale: 0.95,
              }}
              animate={{
                opacity: 1,
                y: 0,
                scale: 1,
                backdropFilter: "blur(0px)",
              }}
              transition={{ duration: 0.5, delay: 0, ease: "easeOut" }}
            >
              <Card
                className="border border-border/60 dark:border-border/40 overflow-hidden relative group"
                style={{
                  boxShadow: "rgba(64, 125, 225, 0.08) 0px 8px 25px",
                }}
              >
                <div className="absolute bg-blue-600/80 size-20 blur-3xl -top-6 group-hover:saturate-150 group-hover:scale-125 transition-all duration-500"></div>
                <CardContent className="p-6 py-8">
                  <div className="flex items-center space-x-4">
                    <div className="p-2 bg-primary/20 rounded-lg brightness-125">
                      <Video className="size-6 text-primary" />
                    </div>
                    <div>
                      <p className="text-xl font-medium text-foreground/70 tracking-tighter">
                        Total Videos
                      </p>
                      <p className="text-2xl font-bold">156</p>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </motion.div>

            <motion.div
              initial={{
                opacity: 0,
                y: 20,
                scale: 0.95,
              }}
              animate={{
                opacity: 1,
                y: 0,
                scale: 1,
                backdropFilter: "blur(0px)",
              }}
              transition={{ duration: 0.5, delay: 0.15, ease: "easeOut" }}
            >
              <Card
                className="border border-border/60 dark:border-border/40 overflow-hidden relative group"
                style={{
                  boxShadow: "rgba(244, 63, 94, 0.08) 0px 8px 25px",
                }}
              >
                <div className="absolute bg-rose-600/80 size-20 blur-3xl -top-6 group-hover:saturate-150 group-hover:scale-125 transition-all duration-500"></div>
                <CardContent className="p-6 py-8">
                  <div className="flex items-center space-x-3">
                    <div className="p-2 bg-rose-400/20 rounded-lg brightness-125">
                      <Heart className="h-5 w-5 text-rose-400" />
                    </div>
                    <div>
                      <p className="text-xl font-medium text-foreground/70 tracking-tighter">
                        Total Likes
                      </p>
                      <p className="text-2xl font-bold">24.5K</p>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </motion.div>

            <motion.div
              initial={{
                opacity: 0,
                y: 20,
                scale: 0.95,
              }}
              animate={{
                opacity: 1,
                y: 0,
                scale: 1,
                backdropFilter: "blur(0px)",
              }}
              transition={{ duration: 0.5, delay: 0.3, ease: "easeOut" }}
            >
              <Card
                className="border border-border/60 dark:border-border/40 overflow-hidden relative group"
                style={{
                  boxShadow: "rgba(250, 173, 20, 0.08) 0px 8px 25px",
                }}
              >
                <div className="absolute bg-yellow-600/80 size-20 blur-3xl -top-6 group-hover:saturate-150 group-hover:scale-125 transition-all duration-500"></div>
                <CardContent className="p-6 py-8">
                  <div className="flex items-center space-x-3">
                    <div className="p-2 bg-yellow-400/20 rounded-lg brightness-125">
                      <MessageCircle className="h-5 w-5 text-yellow-400" />
                    </div>
                    <div>
                      <p className="text-xl font-medium text-foreground/70 tracking-tighter">
                        Total Comments
                      </p>
                      <p className="text-2xl font-bold">1.2K</p>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </motion.div>

            <motion.div
              initial={{
                opacity: 0,
                y: 20,
                scale: 0.95,
              }}
              animate={{
                opacity: 1,
                y: 0,
                scale: 1,
              }}
              transition={{ duration: 0.5, delay: 0.45, ease: "easeOut" }}
            >
              <Card
                className="border border-border/60 dark:border-border/40 overflow-hidden relative group"
                style={{
                  boxShadow: "rgba(34, 197, 94, 0.08) 0px 8px 25px",
                }}
              >
                <div className="absolute bg-lime-600/80 size-20 blur-3xl -top-6 group-hover:saturate-150 group-hover:scale-125 transition-all duration-500"></div>
                <CardContent className="p-6 py-8">
                  <div className="flex items-center space-x-3">
                    <div className="p-2 bg-lime-500/20 rounded-lg brightness-125">
                      <Eye className="h-5 w-5 text-lime-500" />
                    </div>
                    <div>
                      <p className="text-xl font-medium text-foreground/70 tracking-tighter">
                        Avg. Engagement
                      </p>
                      <p className="text-2xl font-bold">6.8%</p>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </motion.div>
          </div>
        </div>

        {/* Chart Section */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.4, ease: "easeOut" }}
        >
          <Card
            className="max-w-7xl mx-auto"
            style={{
              boxShadow: "rgba(0, 0, 0, 0.04) 0px 8px 20px",
            }}
          >
            <CardHeader className="flex flex-col md:flex-row md:items-center justify-between py-6 gap-2">
              <CardTitle className="text-3xl md:text-4xl font-semibold tracking-tighter bg-gradient-to-b from-foreground to-muted-foreground/70 bg-clip-text text-transparent">
                Engagement Overview
              </CardTitle>
              <div className="flex space-x-2">
                {["Week", "Month", "Year"].map((tab) => (
                  <Button
                    key={tab}
                    variant={activeTab === tab ? "default" : "ghost"}
                    size="sm"
                    onClick={() => setActiveTab(tab)}
                    className={
                      activeTab === tab ? "bg-primary hover:bg-primary/80" : ""
                    }
                  >
                    {tab}
                  </Button>
                ))}
              </div>
            </CardHeader>
            <CardContent className="px-2 md:px-6">
              <div className="h-[500px]">
                <ChartContainer
                  config={{
                    likes: {
                      label: "Likes",
                      color: "#ec4899",
                    },
                    comments: {
                      label: "Comments",
                      color: "#06b6d4",
                    },
                    views: {
                      label: "Views",
                      color: "#10b981",
                    },
                  }}
                  className="h-full w-full"
                >
                  <ResponsiveContainer width="100%" height="100%">
                    <AreaChart data={chartData}>
                      <defs>
                        <linearGradient
                          id="fillLikes"
                          x1="0"
                          y1="0"
                          x2="0"
                          y2="1"
                        >
                          <stop
                            offset="1%"
                            stopColor="var(--color-likes)"
                            stopOpacity={0.23}
                          />
                          <stop
                            offset="50%"
                            stopColor="var(--color-likes)"
                            stopOpacity={0}
                          />
                        </linearGradient>
                        <linearGradient
                          id="fillComments"
                          x1="0"
                          y1="0"
                          x2="0"
                          y2="1"
                        >
                          <stop
                            offset="1%"
                            stopColor="var(--color-comments)"
                            stopOpacity={0.2}
                          />
                          <stop
                            offset="50%"
                            stopColor="var(--color-comments)"
                            stopOpacity={0}
                          />
                        </linearGradient>
                        <linearGradient
                          id="fillViews"
                          x1="0"
                          y1="0"
                          x2="0"
                          y2="1"
                        >
                          <stop
                            offset="1%"
                            stopColor="var(--color-views)"
                            stopOpacity={0.2}
                          />
                          <stop
                            offset="50%"
                            stopColor="var(--color-views)"
                            stopOpacity={0}
                          />
                        </linearGradient>
                      </defs>
                      <XAxis
                        dataKey="week"
                        axisLine={false}
                        tickLine={false}
                        tick={{ fill: "#94a3b8", fontSize: 12 }}
                      />
                      <YAxis
                        axisLine={false}
                        tickLine={false}
                        tick={{ fill: "#94a3b8", fontSize: 12 }}
                      />
                      <ChartTooltip
                        content={
                          <ChartTooltipContent className="bg-background/30 py-4 w-40 backdrop-blur-lg text-foreground" />
                        }
                      />
                      <Area
                        type="monotone"
                        dataKey="views"
                        stroke="#10b981"
                        strokeWidth={4}
                        fill="url(#fillViews)"
                        style={{
                          filter: "drop-shadow(0 0 18px #10b981)",
                        }}
                      />
                      <Area
                        type="monotone"
                        dataKey="comments"
                        stroke="#06b6d4"
                        strokeWidth={4}
                        fill="url(#fillComments)"
                        style={{
                          filter: "drop-shadow(0 0 18px #06b6d4)",
                        }}
                      />
                      <Area
                        type="monotone"
                        dataKey="likes"
                        stroke="#ec4899"
                        strokeWidth={4}
                        fill="url(#fillLikes)"
                        style={{
                          filter: "drop-shadow(0 0 18px #ec4899)",
                        }}
                      />
                    </AreaChart>
                  </ResponsiveContainer>
                </ChartContainer>
              </div>
            </CardContent>
          </Card>
        </motion.div>

        {/* Contributors Section */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.6, ease: "easeOut" }}
        >
          <Card
            className="max-w-7xl mx-auto mb-40"
            style={{
              boxShadow: "rgba(0, 0, 0, 0.04) 0px 8px 20px",
            }}
          >
            <CardHeader className="flex flex-col md:flex-row md:items-center justify-between py-6 gap-2 overflow-hidden">
              <CardTitle className="text-3xl md:text-4xl font-semibold tracking-tighter bg-gradient-to-b from-foreground to-muted-foreground/70 bg-clip-text text-transparent">
                Top Contributors
              </CardTitle>
              <div className="flex space-x-2">
                {["All", "Top Performer", "New User"].map((filter) => (
                  <Button
                    key={filter}
                    variant={activeFilter === filter ? "default" : "ghost"}
                    size="sm"
                    onClick={() => setActiveFilter(filter)}
                    className={
                      activeFilter === filter
                        ? "bg-primary hover:bg-primary/90"
                        : ""
                    }
                  >
                    {filter}
                  </Button>
                ))}
              </div>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-3 lg:grid-cols-4 gap-4">
                {contributors.map((contributor, index) => (
                  <motion.div
                    key={index}
                    initial={{ opacity: 0, y: 20, scale: 0.9 }}
                    whileInView={{ opacity: 1, y: 0, scale: 1 }}
                    viewport={{ once: true, amount: 0.3 }}
                    transition={{
                      duration: 0.5,
                      delay: 0.08 * index,
                      ease: "easeOut",
                    }}
                  >
                    <Card className="relative overflow-hidden shadow">
                      <Image
                        src="https://assets.lummi.ai/assets/QmbQb4Sm7v7WZhXyYyorQ4xUoDSznCdiFNwHn9DW8512gA?auto=format&w=1500"
                        alt={contributor.name}
                        width={1500}
                        height={1000}
                        className="object-cover mb-2 absolute inset-0 w-full h-full"
                      />
                      <div className="absolute inset-x-0 w-full h-1/2 bottom-0 bg-gradient-to-b from-transparent via-primary to-primary z-10" />
                      <CardContent className="p-0 relative z-10">
                        <div className="relative p-6 mb-6">
                          <div className="flex flex-col items-center space-y-3 pt-40">
                            {/* <div className="relative">
                          <Avatar className="h-16 w-16">
                            <AvatarImage
                              src={
                                "https://xvatar.vercel.app/api/avatar/" + index
                              }
                            />
                            <AvatarFallback className="bg-gradient-to-br from-card to-background text-foreground">
                              {contributor.name
                                .split(" ")
                                .map((n) => n[0])
                                .join("")}
                            </AvatarFallback>
                          </Avatar>
                          <div className="absolute -bottom-1 -right-1">
                            <Badge
                              className={`bg-primary text-primary-foreground text-xs px-2 py-1 rounded-full border-2 border-background`}
                            >
                              {contributor.badge}
                            </Badge>
                          </div>
                        </div> */}
                            <div className="text-center text-white">
                              <h3 className="font-semibold text-lg text-shadow-lg">
                                {contributor.name}
                              </h3>
                              <p className="opacity-80 text-sm">
                                @
                                {contributor.name
                                  .toLowerCase()
                                  .replace(" ", "")}
                              </p>
                            </div>
                          </div>
                        </div>

                        <div className="px-6 pb-4 space-y-3">
                          <div className="flex items-center justify-between">
                            <span className="text-white/80 text-xs">
                              👁️ {contributor.likes * 2} views
                            </span>
                          </div>

                          <div className="space-y-2">
                            <div className="flex items-center justify-between">
                              <span className="text-white/80 text-xs">
                                ❤️ {contributor.likes} likes
                              </span>
                            </div>
                            <div className="flex items-center justify-between">
                              <span className="text-white/80 text-xs">
                                💬 {Math.floor(contributor.likes * 0.1)}{" "}
                                comments
                              </span>
                            </div>
                            <div className="flex items-center justify-between">
                              <span className="text-white/80 text-xs">
                                📊 {contributor.engagement} eng. rate
                              </span>
                            </div>
                          </div>
                        </div>

                        {/* Footer */}
                        <div className="px-6">
                          <Button className="w-full bg-white text-primary hover:bg-white/90">
                            View Profile
                          </Button>
                        </div>
                      </CardContent>
                    </Card>
                  </motion.div>
                ))}
              </div>
            </CardContent>
          </Card>
        </motion.div>
      </div>
    </div>
  );
}

const contributors = [
  {
    name: "Sarah Johnson",
    avatar: "/placeholder.svg?height=40&width=40",
    badge: "Top Performer",
    badgeColor: "bg-green-500",
    videos: 12,
    likes: 2450,
    engagement: "8.5%",
  },
  {
    name: "Michael Chen",
    avatar: "/placeholder.svg?height=40&width=40",
    badge: "New User",
    badgeColor: "bg-blue-500",
    videos: 8,
    likes: 1200,
    engagement: "6.2%",
  },
  {
    name: "Emma Wilson",
    avatar: "/placeholder.svg?height=40&width=40",
    badge: "Low Engagement",
    badgeColor: "bg-orange-500",
    videos: 15,
    likes: 800,
    engagement: "4.8%",
  },
  {
    name: "Sarah Johnson",
    avatar: "/placeholder.svg?height=40&width=40",
    badge: "Top Performer",
    badgeColor: "bg-green-500",
    videos: 12,
    likes: 2450,
    engagement: "8.5%",
  },
  {
    name: "Michael Chen",
    avatar: "/placeholder.svg?height=40&width=40",
    badge: "New User",
    badgeColor: "bg-blue-500",
    videos: 8,
    likes: 1200,
    engagement: "6.2%",
  },
  {
    name: "Emma Wilson",
    avatar: "/placeholder.svg?height=40&width=40",
    badge: "Low Engagement",
    badgeColor: "bg-orange-500",
    videos: 15,
    likes: 800,
    engagement: "4.8%",
  },
  {
    name: "Sarah Johnson",
    avatar: "/placeholder.svg?height=40&width=40",
    badge: "Top Performer",
    badgeColor: "bg-green-500",
    videos: 12,
    likes: 2450,
    engagement: "8.5%",
  },
  {
    name: "Michael Chen",
    avatar: "/placeholder.svg?height=40&width=40",
    badge: "New User",
    badgeColor: "bg-blue-500",
    videos: 8,
    likes: 1200,
    engagement: "6.2%",
  },
];
