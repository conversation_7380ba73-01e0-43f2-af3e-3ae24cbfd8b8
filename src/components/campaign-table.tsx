"use client";

import { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { motion } from "framer-motion";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Avatar, AvatarFallback } from "@/components/ui/avatar";
import { Card, CardContent } from "@/components/ui/card";
import {
  Edit,
  Copy,
  Trash2,
  Facebook,
  Instagram,
  Twitter,
  Linkedin,
  Youtube,
  ChevronLeft,
  ChevronRight,
  Plus,
} from "lucide-react";
import {
  SectionCards,
  defaultSectionCardsData,
} from "@/components/section-cards";

interface Campaign {
  id: string;
  name: string;
  platform: string;
  status: "active" | "paused" | "completed" | "draft";
  assignedUsers: string[];
  reach: number;
  createdDate: string;
}

const mockCampaigns: Campaign[] = [
  {
    id: "1",
    name: "Summer Sale Campaign",
    platform: "facebook",
    status: "active",
    assignedUsers: ["John Doe", "Sarah Wilson"],
    reach: 125420,
    createdDate: "23.03.2024",
  },
  {
    id: "2",
    name: "Brand Awareness Drive",
    platform: "instagram",
    status: "paused",
    assignedUsers: ["Mike Johnson"],
    reach: 89650,
    createdDate: "22.03.2024",
  },
  {
    id: "3",
    name: "Product Launch",
    platform: "twitter",
    status: "active",
    assignedUsers: ["Emma Davis", "Alex Chen", "Lisa Park"],
    reach: 67890,
    createdDate: "20.03.2024",
  },
  {
    id: "4",
    name: "Holiday Special",
    platform: "linkedin",
    status: "completed",
    assignedUsers: ["Robert Smith"],
    reach: 45230,
    createdDate: "18.03.2024",
  },
  {
    id: "5",
    name: "Video Marketing",
    platform: "youtube",
    status: "draft",
    assignedUsers: ["Jennifer Lee", "David Wilson"],
    reach: 23450,
    createdDate: "15.03.2024",
  },
  {
    id: "6",
    name: "Influencer Collaboration",
    platform: "instagram",
    status: "active",
    assignedUsers: ["Sophie Turner"],
    reach: 156780,
    createdDate: "13.03.2024",
  },
  {
    id: "7",
    name: "Black Friday Campaign",
    platform: "facebook",
    status: "completed",
    assignedUsers: ["Tom Anderson", "Kate Brown"],
    reach: 234560,
    createdDate: "11.03.2024",
  },
  {
    id: "8",
    name: "Newsletter Signup",
    platform: "twitter",
    status: "paused",
    assignedUsers: ["Chris Martin"],
    reach: 34567,
    createdDate: "14.01.2024",
  },
];

const platformIcons = {
  facebook: Facebook,
  instagram: Instagram,
  twitter: Twitter,
  linkedin: Linkedin,
  youtube: Youtube,
};

const platformConfig = {
  facebook: { color: "hsl(221, 83%, 53%)" },
  instagram: { color: "hsl(329, 70%, 58%)" },
  twitter: { color: "hsl(203, 89%, 53%)" },
  linkedin: { color: "hsl(201, 100%, 35%)" },
  youtube: { color: "hsl(0, 100%, 50%)" },
};

export default function CampaignTable() {
  const [campaigns, setCampaigns] = useState<Campaign[]>(mockCampaigns);
  const [editingCampaign, setEditingCampaign] = useState<Campaign | null>(null);
  const [deletingCampaign, setDeletingCampaign] = useState<Campaign | null>(
    null
  );
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [currentPage, setCurrentPage] = useState(1);
  const itemsPerPage = 5;

  // Pagination logic
  const totalPages = Math.ceil(campaigns.length / itemsPerPage);
  const startIndex = (currentPage - 1) * itemsPerPage;
  const endIndex = startIndex + itemsPerPage;
  const currentCampaigns = campaigns.slice(startIndex, endIndex);

  const handleEdit = (campaign: Campaign) => {
    setEditingCampaign({ ...campaign });
    setIsEditDialogOpen(true);
  };

  const handleSaveEdit = () => {
    if (editingCampaign) {
      setCampaigns(
        campaigns.map((c) =>
          c.id === editingCampaign.id ? editingCampaign : c
        )
      );
      setIsEditDialogOpen(false);
      setEditingCampaign(null);
    }
  };

  const handleDelete = (campaign: Campaign) => {
    setDeletingCampaign(campaign);
    setIsDeleteDialogOpen(true);
  };

  const confirmDelete = () => {
    if (deletingCampaign) {
      setCampaigns(campaigns.filter((c) => c.id !== deletingCampaign.id));
      setIsDeleteDialogOpen(false);
      setDeletingCampaign(null);
      // Adjust current page if necessary
      const newTotalPages = Math.ceil((campaigns.length - 1) / itemsPerPage);
      if (currentPage > newTotalPages && newTotalPages > 0) {
        setCurrentPage(newTotalPages);
      }
    }
  };

  const handleCopy = (campaign: Campaign) => {
    const newCampaign = {
      ...campaign,
      id: Date.now().toString(),
      name: `${campaign.name} (Copy)`,
      status: "draft" as const,
    };
    setCampaigns([newCampaign, ...campaigns]);
  };

  const formatReach = (reach: number) => {
    if (reach >= 1000000) {
      return `${(reach / 1000000).toFixed(1)}M`;
    } else if (reach >= 1000) {
      return `${(reach / 1000).toFixed(1)}K`;
    }
    return reach.toString();
  };

  const getUserInitials = (name: string) => {
    return name
      .split(" ")
      .map((n) => n[0])
      .join("")
      .toUpperCase();
  };

  // Vibrant mono color for status text
  const getStatusTextColor = (status: Campaign["status"]) => {
    switch (status) {
      case "active":
        return "text-green-600 font-semibold";
      case "paused":
        return "text-yellow-600 font-semibold";
      case "completed":
        return "text-blue-600 font-semibold";
      case "draft":
        return "text-gray-500 font-semibold";
      default:
        return "text-gray-500 font-semibold";
    }
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <motion.div
      transition={{ duration: 0.3 }}
      initial={{ opacity: 0, y: -20 }}
      animate={{ opacity: 1, y: 0 }}
      className="flex flex-col md:flex-row md:items-center md:justify-between space-y-2 md:space-y-0">
        <div>
          <h1 className="text-3xl font-bold tracking-tight text-foreground">
            Campaign Management
          </h1>
          <p className="text-muted-foreground">
            Manage your marketing campaigns across different platforms and track
            their performance.
          </p>
        </div>
        <Button className="mt-2 md:mt-0 flex items-center gap-2">
          <Plus className="h-4 w-4" />
          New Campaign
        </Button>
      </motion.div>

      {/* Stats Cards */}
      <SectionCards data={defaultSectionCardsData} />

      {/* Main Table Card */}
      <Card className="p-2 [box-shadow:0_-40px_80px_-40px_#2664ff2f_inset] bg-transparent">
        <CardContent className="p-0">
          <div className="overflow-x-auto">
            <Table className="border-separate border-spacing-y-4">
              <TableHeader className="pointer-events-none">
                <TableRow>
                  <TableHead>Campaign</TableHead>
                  <TableHead>Platform</TableHead>
                  <TableHead>Status</TableHead>
                  <TableHead>Assigned Users</TableHead>
                  <TableHead>Reach</TableHead>
                  <TableHead className="text-right">Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {currentCampaigns.map((campaign, idx) => {
                  const PlatformIcon =
                    platformIcons[
                      campaign.platform as keyof typeof platformIcons
                    ];
                  const platformInfo =
                    platformConfig[
                      campaign.platform as keyof typeof platformConfig
                    ];
                  return (
                    <motion.tr
                      key={campaign.id}
                      initial={{ opacity: 0, y: 20 }}
                      animate={{ opacity: 1, y: 0 }}
                      transition={{ duration: 0.3, delay: idx * 0.07 }}
                      className="hover:bg-muted/50 transition-colors bg-muted/20 dark:bg-secondary"
                    >
                      <TableCell className="rounded-l-md">
                        <div className="space-y-1">
                          <div className="font-medium">{campaign.name}</div>
                          <div className="text-sm text-muted-foreground">
                            {campaign.createdDate}
                          </div>
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center gap-3">
                          <div
                            className="flex h-8 w-8 items-center justify-center rounded-lg text-white"
                            style={{ backgroundColor: platformInfo.color }}
                          >
                            <PlatformIcon className="h-4 w-4" />
                          </div>
                        </div>
                      </TableCell>
                      <TableCell>
                        {/* Vibrant mono color text for status, no badge */}
                        <span className={getStatusTextColor(campaign.status)}>
                          {campaign.status.charAt(0).toUpperCase() +
                            campaign.status.slice(1)}
                        </span>
                      </TableCell>
                      <TableCell>
                        <div className="flex -space-x-2">
                          {campaign.assignedUsers.slice(0, 2).map((user) => (
                            <Avatar
                              key={user}
                              className="h-8 w-8 border-2 border-background"
                            >
                              <AvatarFallback className="text-xs">
                                {getUserInitials(user)}
                              </AvatarFallback>
                            </Avatar>
                          ))}
                          {campaign.assignedUsers.length > 2 && (
                            <div className="flex h-8 w-8 items-center justify-center rounded-full border-2 border-background bg-muted">
                              <span className="text-xs text-muted-foreground">
                                +{campaign.assignedUsers.length - 2}
                              </span>
                            </div>
                          )}
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="space-y-1">
                          <div className="font-medium">
                            {formatReach(campaign.reach)}
                          </div>
                          <div className="text-sm text-muted-foreground">
                            Total reach
                          </div>
                        </div>
                      </TableCell>
                      <TableCell className="rounded-r-md">
                        <div className="flex items-center justify-end gap-1">
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => handleEdit(campaign)}
                            className="h-8 w-8 p-0"
                          >
                            <Edit className="h-4 w-4" />
                            <span className="sr-only">Edit campaign</span>
                          </Button>
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => handleCopy(campaign)}
                            className="h-8 w-8 p-0"
                          >
                            <Copy className="h-4 w-4" />
                            <span className="sr-only">Copy campaign</span>
                          </Button>
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => handleDelete(campaign)}
                            className="h-8 w-8 p-0 text-destructive hover:text-destructive"
                          >
                            <Trash2 className="h-4 w-4" />
                            <span className="sr-only">Delete campaign</span>
                          </Button>
                        </div>
                      </TableCell>
                    </motion.tr>
                  );
                })}
              </TableBody>
            </Table>
          </div>

          {/* Pagination */}
          <div className="flex items-center flex-col sm:flex-row justify-between border-t px-6 gap-4 pt-4">
            <div className="text-sm text-muted-foreground">
              Showing {startIndex + 1} to {Math.min(endIndex, campaigns.length)}{" "}
              of {campaigns.length} campaigns
            </div>
            <div className="flex items-center gap-2 mb-2">
              <Button
                variant="outline"
                size="sm"
                onClick={() => setCurrentPage(Math.max(1, currentPage - 1))}
                disabled={currentPage === 1}
              >
                <ChevronLeft className="h-4 w-4" />
                Previous
              </Button>
              <div className="flex items-center gap-1">
                {Array.from({ length: totalPages }, (_, i) => i + 1).map(
                  (page) => (
                    <Button
                      key={page}
                      variant={currentPage === page ? "default" : "outline"}
                      size="sm"
                      onClick={() => setCurrentPage(page)}
                      className="h-8 w-8 p-0"
                    >
                      {page}
                    </Button>
                  )
                )}
              </div>
              <Button
                variant="outline"
                size="sm"
                onClick={() =>
                  setCurrentPage(Math.min(totalPages, currentPage + 1))
                }
                disabled={currentPage === totalPages}
              >
                Next
                <ChevronRight className="h-4 w-4" />
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Edit Dialog */}
      <Dialog open={isEditDialogOpen} onOpenChange={setIsEditDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Edit Campaign</DialogTitle>
            <DialogDescription>
              Make changes to your campaign here. Click save when you&apos;re
              done.
            </DialogDescription>
          </DialogHeader>
          {editingCampaign && (
            <div className="grid gap-4 py-4">
              <div className="grid gap-2">
                <Label htmlFor="name">Campaign Name</Label>
                <Input
                  id="name"
                  value={editingCampaign.name}
                  onChange={(e) =>
                    setEditingCampaign({
                      ...editingCampaign,
                      name: e.target.value,
                    })
                  }
                />
              </div>
              <div className="grid gap-2">
                <Label htmlFor="platform">Platform</Label>
                <Select
                  value={editingCampaign.platform}
                  onValueChange={(value) =>
                    setEditingCampaign({
                      ...editingCampaign,
                      platform: value,
                    })
                  }
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="facebook">Facebook</SelectItem>
                    <SelectItem value="instagram">Instagram</SelectItem>
                    <SelectItem value="twitter">Twitter</SelectItem>
                    <SelectItem value="linkedin">LinkedIn</SelectItem>
                    <SelectItem value="youtube">YouTube</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div className="grid gap-2">
                <Label htmlFor="status">Status</Label>
                <Select
                  value={editingCampaign.status}
                  onValueChange={(value) =>
                    setEditingCampaign({
                      ...editingCampaign,
                      status: value as Campaign["status"],
                    })
                  }
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="active">Active</SelectItem>
                    <SelectItem value="paused">Paused</SelectItem>
                    <SelectItem value="completed">Completed</SelectItem>
                    <SelectItem value="draft">Draft</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div className="grid gap-2">
                <Label htmlFor="reach">Reach</Label>
                <Input
                  id="reach"
                  type="number"
                  value={editingCampaign.reach}
                  onChange={(e) =>
                    setEditingCampaign({
                      ...editingCampaign,
                      reach: Number.parseInt(e.target.value) || 0,
                    })
                  }
                />
              </div>
            </div>
          )}
          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => setIsEditDialogOpen(false)}
            >
              Cancel
            </Button>
            <Button onClick={handleSaveEdit}>Save Changes</Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Delete Alert Dialog */}
      <AlertDialog
        open={isDeleteDialogOpen}
        onOpenChange={setIsDeleteDialogOpen}
      >
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Are you absolutely sure?</AlertDialogTitle>
            <AlertDialogDescription>
              This action cannot be undone. This will permanently delete the
              campaign &quot;{deletingCampaign?.name}&quot; and remove all
              associated data.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction
              onClick={confirmDelete}
              className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
            >
              Delete Campaign
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  );
}
