"use client";
import { useState } from "react";
import { motion } from "framer-motion";
import {
  Facebook,
  Instagram,
  Twitter,
  Linkedin,
  Youtube,
  CheckCircle,
  Trophy,
  ThumbsUp,
  Search,
  Grid,
  List,
} from "lucide-react";
import clsx from "clsx";
import { But<PERSON> } from "./ui/button";

// Mock campaign data
const campaigns = [
  {
    id: 1,
    platform: "instagram",
    status: "Active",
    title: "Summer Collection Launch",
    description:
      "Summer collection promotion campaign with influencer collaboration",
    reach: 128500,
    likes: 8900,
    comments: 423,
    progress: 0.8,
  },
  {
    id: 2,
    platform: "tiktok",
    status: "Active",
    title: "Product Tutorial Series",
    description: "Educational content showcasing product features and usage",
    reach: 92100,
    likes: 4500,
    comments: 289,
    progress: 0.6,
  },
  {
    id: 3,
    platform: "youtube",
    status: "Completed",
    title: "Brand Story Video",
    description: "Behind-the-scenes look at our brand's journey and values",
    reach: 45200,
    likes: 2300,
    comments: 156,
    progress: 1,
  },
  {
    id: 4,
    platform: "facebook",
    status: "Active",
    title: "Community Event",
    description: "Virtual community engagement event with live Q&A",
    reach: 34700,
    likes: 1800,
    comments: 167,
    progress: 0.5,
  },
  {
    id: 5,
    platform: "instagram",
    status: "Active",
    title: "Holiday Special",
    description: "Holiday season promotion with special offers",
    reach: 67300,
    likes: 3200,
    comments: 245,
    progress: 0.7,
  },
  {
    id: 6,
    platform: "tiktok",
    status: "Active",
    title: "User Generated Content",
    description: "UGC campaign encouraging user participation",
    reach: 89100,
    likes: 5600,
    comments: 378,
    progress: 0.9,
  },
];

const platformIcons: Record<string, any> = {
  facebook: Facebook,
  instagram: Instagram,
  twitter: Twitter,
  linkedin: Linkedin,
  youtube: Youtube,
  tiktok: (props: any) => (
    <svg {...props} viewBox="0 0 24 24" fill="none">
      <path
        d="M17 2v2.5a4.5 4.5 0 0 0 4.5 4.5h.5v3a7 7 0 1 1-7-7h2Z"
        stroke="currentColor"
        strokeWidth="2"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  ),
};

const platformColors: Record<string, string> = {
  facebook: "text-blue-500",
  instagram: "text-pink-500",
  twitter: "text-sky-400",
  linkedin: "text-blue-700",
  youtube: "text-red-500",
  tiktok: "text-black dark:text-white",
};

function formatNumber(n: number) {
  if (n >= 1_000_000) return (n / 1_000_000).toFixed(1) + "M";
  if (n >= 1_000) return (n / 1_000).toFixed(1) + "K";
  return n.toString();
}

const container = {
  hidden: { opacity: 0 },
  show: {
    opacity: 1,
    transition: {
      staggerChildren: 0.1,
    },
  },
};

const item = {
  hidden: { opacity: 0, y: 20 },
  show: { opacity: 1, y: 0, transition: { duration: 0.5 } },
};

const statCard = {
  hidden: { opacity: 0, y: -20 },
  show: (i: number) => ({
    opacity: 1,
    y: 0,
    transition: { delay: i * 0.1, duration: 0.5 },
  }),
  hover: { scale: 1.04 },
};

const cardHover = {
  hover: {
    y: -5,
    boxShadow:
      "0 10px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04)",
  },
};

export default function ClientDashboard() {
  const [view, setView] = useState<"grid" | "list">("grid");

  return (
    <motion.div
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      transition={{ duration: 0.5 }}
      className="min-h-screen mb-12"
    >
      {/* Stat Cards */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
        <motion.div
          custom={0}
          variants={statCard}
          initial="hidden"
          animate="show"
          whileHover="hover"
          className="rounded-3xl bg-gradient-to-br from-blue-100/70 via-white/60 to-blue-200/40 dark:from-blue-900/30 dark:via-white/10 dark:to-blue-900/10 backdrop-blur-xl border shadow-xl p-8 flex items-center gap-5"
        >
          <motion.div
            whileHover={{ rotate: 10 }}
            className="bg-blue-500/20 rounded-2xl p-4 flex items-center justify-center"
          >
            <Grid className="text-blue-500 w-8 h-8" />
          </motion.div>
          <div>
            <div className="text-3xl font-extrabold tracking-tight text-blue-900 dark:text-blue-200">
              24
            </div>
            <div className="text-blue-700/70 dark:text-blue-200/70 text-base font-medium">
              Active Campaigns
            </div>
          </div>
        </motion.div>

        <motion.div
          custom={1}
          variants={statCard}
          initial="hidden"
          animate="show"
          whileHover="hover"
          className="rounded-3xl bg-gradient-to-br from-purple-100/70 via-white/60 to-purple-200/40 dark:from-purple-900/30 dark:via-white/10 dark:to-purple-900/10 backdrop-blur-xl border shadow-xl p-8 flex items-center gap-5"
        >
          <motion.div
            whileHover={{ rotate: 10 }}
            className="bg-purple-500/20 rounded-2xl p-4 flex items-center justify-center"
          >
            <Trophy className="text-purple-500 w-8 h-8" />
          </motion.div>
          <div>
            <div className="text-3xl font-extrabold tracking-tight text-purple-900 dark:text-purple-200">
              Summer 2024
            </div>
            <div className="text-purple-700/70 dark:text-purple-200/70 text-base font-medium">
              Best Performing
            </div>
          </div>
        </motion.div>

        <motion.div
          custom={2}
          variants={statCard}
          initial="hidden"
          animate="show"
          whileHover="hover"
          className="rounded-3xl bg-gradient-to-br from-emerald-100/70 via-white/60 to-emerald-200/40 dark:from-emerald-900/30 dark:via-white/10 dark:to-emerald-900/10 backdrop-blur-xl border shadow-xl p-8 flex items-center gap-5"
        >
          <motion.div
            whileHover={{ rotate: 10 }}
            className="bg-emerald-500/20 rounded-2xl p-4 flex items-center justify-center"
          >
            <ThumbsUp className="text-emerald-500 w-8 h-8" />
          </motion.div>
          <div>
            <div className="text-3xl font-extrabold tracking-tight text-emerald-900 dark:text-emerald-200">
              {formatNumber(1200000)}
            </div>
            <div className="text-emerald-700/70 dark:text-emerald-200/70 text-base font-medium">
              Interactions
            </div>
          </div>
        </motion.div>
      </div>

      {/* Filters */}
      <motion.div
        initial={{ opacity: 0, y: -20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.2 }}
        className="flex flex-wrap gap-3 mb-8"
      >
        <motion.button
          whileHover={{ scale: 1.05 }}
          whileTap={{ scale: 0.95 }}
          className="bg-white/10 border px-4 py-2 rounded-lg hover:bg-white/20 transition"
        >
          All Platforms
        </motion.button>
        <motion.button
          whileHover={{ scale: 1.05 }}
          whileTap={{ scale: 0.95 }}
          className="bg-white/10 border px-4 py-2 rounded-lg hover:bg-white/20 transition"
        >
          Status: Active
        </motion.button>
        <motion.button
          whileHover={{ scale: 1.05 }}
          whileTap={{ scale: 0.95 }}
          className="bg-white/10 border px-4 py-2 rounded-lg hover:bg-white/20 transition"
        >
          Engagement: High
        </motion.button>
        <div className="flex sm:ml-auto gap-2">
          <motion.button
            whileHover={{ scale: 1.1 }}
            whileTap={{ scale: 0.9 }}
            className={clsx(
              "w-9 h-9 rounded-lg flex items-center justify-center border",
              view === "grid" ? "bg-primary/80 border-primary " : "bg-white/10 "
            )}
            onClick={() => setView("grid")}
          >
            <Grid size={18} />
          </motion.button>
          <motion.button
            whileHover={{ scale: 1.1 }}
            whileTap={{ scale: 0.9 }}
            className={clsx(
              "w-9 h-9 rounded-lg flex items-center justify-center border",
              view === "list" ? "bg-primary/80 border-primary " : "bg-white/10 "
            )}
            onClick={() => setView("list")}
          >
            <List size={18} />
          </motion.button>
        </div>
      </motion.div>

      {/* Campaign Cards */}
      <motion.div
        variants={container}
        initial="hidden"
        animate="show"
        className={clsx(
          view === "grid"
            ? "grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6"
            : "flex flex-col gap-4"
        )}
      >
        {campaigns.map((c) => {
          const Icon = platformIcons[c.platform] || Instagram;
          const cardGradient =
            "border border-border bg-gradient-to-b from-secondary/10 to-secondary p-10 shadow-[0px_2px_0px_0px_rgba(255,255,255,0.1)_inset]";
          const cardShadow =
            "shadow-[0_4px_32px_0_rgba(30,64,175,0.10),0_1.5px_6px_0_rgba(30,64,175,0.10)]";
          const cardBase = "rounded-2xl transition-all";
          const cardContent = "flex flex-col h-full px-6 py-8";

          // Responsive list view classes
          const listView =
            "flex flex-col sm:flex-row items-start sm:items-center gap-4 sm:gap-6 px-6 py-8";

          if (view === "list") {
            return (
              <motion.div
                key={c.id}
                variants={item}
                whileHover={cardHover}
                className={clsx(
                  "group justify-between w-full cursor-pointer relative overflow-hidden",
                  cardGradient,
                  cardShadow,
                  cardBase,
                  listView
                )}
              >
                <motion.div
                  initial={{ scale: 0 }}
                  animate={{ scale: 1 }}
                  transition={{ delay: 0.2 }}
                  className="absolute -left-5 -top-5 -z-10 h-40 w-40 rounded-full bg-gradient-to-b from-primary/20 to-secondary blur-md"
                ></motion.div>
                {/* Platform Icon & Status */}
                <div className="flex items-center gap-5 min-w-[120px] sm:min-w-[150px]">
                  <motion.div whileHover={{ rotate: 15 }}>
                    <Icon
                      className={clsx(
                        "w-7 h-7",
                        platformColors[c.platform] || ""
                      )}
                    />
                  </motion.div>
                  <div className="flex flex-col gap-2">
                    <span className="font-medium text-sm">
                      {c.platform.charAt(0).toUpperCase() + c.platform.slice(1)}
                    </span>
                    <span
                      className={clsx(
                        "px-2 py-1 rounded-full text-xs font-semibold shadow w-fit",
                        c.status === "Active"
                          ? "bg-green-500/20 text-emerald-400"
                          : "bg-gray-500/20 text-gray-400"
                      )}
                    >
                      {c.status}
                    </span>
                  </div>
                </div>

                {/* Title & Description */}
                <div className="flex-1 min-w-0 flex flex-col gap-1 sm:ml-4">
                  <motion.div
                    whileHover={{ x: 2 }}
                    className="font-bold text-lg tracking-tight truncate"
                  >
                    {c.title}
                  </motion.div>
                  <div className="text-foreground/70 text-sm line-clamp-2">
                    {c.description}
                  </div>
                </div>

                {/* Stats */}
                <div className="flex flex-wrap gap-3 sm:gap-4 items-center sm:min-w-[180px]">
                  <motion.div
                    whileHover={{ scale: 1.05 }}
                    className="flex items-center justify-end gap-1 text-blue-500 text-sm font-medium"
                  >
                    <ThumbsUp size={16} />
                    {formatNumber(c.reach)}
                  </motion.div>
                  <motion.div
                    whileHover={{ scale: 1.05 }}
                    className="flex items-center gap-1 text-emerald-400 text-sm font-medium"
                  >
                    <CheckCircle size={16} />
                    {formatNumber(c.likes)}
                  </motion.div>
                  <motion.div
                    whileHover={{ scale: 1.05 }}
                    className="flex items-center gap-1 text-purple-400 text-sm font-medium"
                  >
                    <svg
                      width="16"
                      height="16"
                      fill="none"
                      className="text-purple-400"
                      viewBox="0 0 24 24"
                    >
                      <path
                        d="M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2v10Z"
                        stroke="currentColor"
                        strokeWidth="2"
                        strokeLinecap="round"
                        strokeLinejoin="round"
                      />
                    </svg>
                    {formatNumber(c.comments)}
                  </motion.div>
                </div>

                {/* Progress Bar */}
                <div className="w-full sm:w-48 h-2 bg-secondary/40 rounded-full overflow-visible relative z-20">
                  <motion.div
                    initial={{ width: 0 }}
                    animate={{ width: `${Math.round(c.progress * 100)}%` }}
                    transition={{ delay: 0.3, duration: 1 }}
                    className="h-full bg-gradient-to-r from-emerald-300 via-emerald-400 to-emerald-500"
                    style={{
                      borderRadius: "9999px",
                      filter:
                        "drop-shadow(0 10px 12px rgba(16, 185, 129, 0.8))",
                    }}
                  />
                </div>
                <motion.div
                  whileHover={{ scale: 1.03 }}
                  whileTap={{ scale: 0.97 }}
                  className="w-full sm:w-fit mt-6 sm:mt-0"
                >
                  <Button className="w-full font-semibold rounded-lg px-4 shadow transition z-20 relative">
                    View Campaign
                  </Button>
                </motion.div>
              </motion.div>
            );
          }
          // Grid view (default)
          return (
            <motion.div
              key={c.id}
              variants={item}
              whileHover={cardHover}
              className={clsx(
                "group relative justify-between flex w-full cursor-pointer overflow-hidden",
                cardGradient,
                cardShadow,
                cardBase,
                cardContent
              )}
            >
              <motion.div
                initial={{ scale: 0 }}
                animate={{ scale: 1 }}
                transition={{ delay: 0.2 }}
                className="absolute -left-5 -top-5 -z-10 h-40 w-40 rounded-full bg-gradient-to-b from-primary/20 to-secondary blur-md"
              ></motion.div>
              {/* Platform Icon & Status */}
              <div className="flex items-center gap-3 mb-2">
                <motion.div whileHover={{ rotate: 15 }}>
                  <Icon
                    className={clsx(
                      "w-7 h-7",
                      platformColors[c.platform] || ""
                    )}
                  />
                </motion.div>
                <span className="/90 font-medium text-base">
                  {c.platform.charAt(0).toUpperCase() + c.platform.slice(1)}
                </span>
                <span
                  className={clsx(
                    "ml-auto px-3 py-1 rounded-full text-xs font-semibold shadow",
                    c.status === "Active"
                      ? "bg-green-500/20 text-emerald-400"
                      : "bg-gray-500/20 text-gray-400"
                  )}
                >
                  {c.status}
                </span>
              </div>
              {/* Title & Description */}
              <div className="mb-2">
                <motion.div
                  whileHover={{ x: 2 }}
                  className="font-bold text-lg tracking-tight"
                >
                  {c.title}
                </motion.div>
                <div className="text-foreground/70 text-sm">
                  {c.description}
                </div>
              </div>
              {/* Stats */}
              <div className="flex gap-6 items-center mt-2 mb-3">
                <motion.div
                  whileHover={{ scale: 1.05 }}
                  className="flex items-center gap-1 text-blue-500 text-sm font-medium"
                >
                  <ThumbsUp size={16} />
                  {formatNumber(c.reach)}
                </motion.div>
                <motion.div
                  whileHover={{ scale: 1.05 }}
                  className="flex items-center gap-1 text-emerald-400 text-sm font-medium"
                >
                  <CheckCircle size={16} />
                  {formatNumber(c.likes)}
                </motion.div>
                <motion.div
                  whileHover={{ scale: 1.05 }}
                  className="flex items-center gap-1 text-purple-400 text-sm font-medium"
                >
                  <svg
                    width="16"
                    height="16"
                    fill="none"
                    className="text-purple-400"
                    viewBox="0 0 24 24"
                  >
                    <path
                      d="M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2v10Z"
                      stroke="currentColor"
                      strokeWidth="2"
                      strokeLinecap="round"
                      strokeLinejoin="round"
                    />
                  </svg>
                  {formatNumber(c.comments)}
                </motion.div>
              </div>
              {/* Progress Bar */}
              <div className="w-full h-2 bg-secondary/40 rounded-full overflow-visible relative z-20 mb-4">
                <motion.div
                  initial={{ width: 0 }}
                  animate={{ width: `${Math.round(c.progress * 100)}%` }}
                  transition={{ delay: 0.3, duration: 1 }}
                  className="h-full bg-gradient-to-r from-emerald-300 via-emerald-400 to-emerald-500"
                  style={{
                    borderRadius: "9999px",
                    filter: "drop-shadow(0 10px 12px rgba(16, 185, 129, 0.8))",
                  }}
                />
              </div>
              {/* View Campaign Button */}
              <motion.div
                whileHover={{ scale: 1.03 }}
                whileTap={{ scale: 0.97 }}
              >
                <Button className="mt-6 w-full font-semibold rounded-lg px-4 shadow transition z-20 relative">
                  View Campaign
                </Button>
              </motion.div>
            </motion.div>
          );
        })}
      </motion.div>
    </motion.div>
  );
}
