"use client"

import { SidebarIcon } from "lucide-react"

import {
  B<PERSON>crumb,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbPage,
  BreadcrumbSeparator,
} from "@/components/ui/breadcrumb"
import { Avatar, AvatarFallback } from "@/components/ui/avatar"
import { Button } from "@/components/ui/button"
import { Separator } from "@/components/ui/separator"
import ThemeTogglebutton from "./ui/ThemeToggle"

const user = {
  name: "shadcn",
  email: "<EMAIL>",
  avatar: "/avatars/shadcn.jpg",
}

export function SiteHeader() {
  return (
    <header className="bg-background sticky top-0 z-50 flex w-full items-center border-b">
      <div className="flex h-(--header-height) w-full items-center gap-2 px-4">
        <Breadcrumb className="hidden sm:block">
          <BreadcrumbList>
            <BreadcrumbItem>
              <BreadcrumbLink href="#">
                Admin
              </BreadcrumbLink>
            </BreadcrumbItem>
            <BreadcrumbSeparator />
            <BreadcrumbItem>
              <BreadcrumbPage>Campaign Management</BreadcrumbPage>
            </BreadcrumbItem>
          </BreadcrumbList>
        </Breadcrumb>
        <div className="flex items-center gap-2 ml-auto">
          <ThemeTogglebutton />
          <div className="relative group">
            <Button variant="ghost" size="icon" className="rounded-full">
              <Avatar className="h-8 w-8">
                <AvatarFallback>{user.name[0].toUpperCase()}</AvatarFallback>
              </Avatar>
            </Button>
            <div className="absolute right-0 mt-2 hidden min-w-[180px] rounded-md bg-popover p-2 shadow-lg group-hover:block">
              <div className="px-2 py-1 text-sm font-medium">{user.name}</div>
              <div className="px-2 pb-2 text-xs text-muted-foreground">{user.email}</div>
              <Button variant="ghost" className="w-full justify-start">Profile</Button>
              <Button variant="ghost" className="w-full justify-start">Logout</Button>
            </div>
          </div>
        </div>
      </div>
    </header>
  )
}
