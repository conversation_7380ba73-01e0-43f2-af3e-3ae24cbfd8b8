"use client";

import { motion } from "framer-motion";

interface UserCardData {
  name: string;
  role: string;
  activeCampaigns: number;
  totalReach: string;
}

const users: UserCardData[] = [
  {
    name: "<PERSON><PERSON>",
    role: "Influencer",
    activeCampaigns: 5,
    totalReach: "89.3K",
  },
  {
    name: "<PERSON>",
    role: "Photographer",
    activeCampaigns: 2,
    totalReach: "210.1K",
  },
  {
    name: "<PERSON>",
    role: "Content Strategist",
    activeCampaigns: 4,
    totalReach: "132.7K",
  },
  {
    name: "<PERSON>",
    role: "YouTuber",
    activeCampaigns: 1,
    totalReach: "354.6K",
  },
  {
    name: "<PERSON>",
    role: "Tech Reviewer",
    activeCampaigns: 3,
    totalReach: "178.9K",
  },
  {
    name: "<PERSON>",
    role: "Travel Blogger",
    activeCampaigns: 6,
    totalReach: "241.5K",
  },
];

export default function UserManagementCards() {
  return (
    <div className="mt-16">
      <div className="mx-auto max-w-7xl">
        <div className="flex flex-col sm:flex-row justify-between w-full mb-6">
          <h2 className="text-foreground text-xl w-full mb-2 font-semibold">
            User Management
          </h2>
          <div className="flex flex-col justify-end sm:flex-row w-full gap-2">
            <input
              type="text"
              placeholder="Search users..."
              className="bg-secondary text-foreground px-3 py-2 rounded-md outline-none border-none"
            />
            <button className="bg-primary hover:bg-primary/90 text-primary-foreground px-4 py-2 rounded-md font-medium">
              Bulk Message
            </button>
          </div>
        </div>
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6">
          {users.map((user, idx) => {
            const barValues = Array.from({ length: 8 }, () =>
              Math.floor(Math.random() * 20 + 11)
            );

            // Dynamic color based on active campaigns
            const gradientColor =
              user.activeCampaigns >= 5
                ? ["#ff6a00", "#ee0979"] // orange-pink
                : user.activeCampaigns >= 3
                ? ["#4facfe", "#00f2fe"] // blue-cyan
                : ["#43e97b", "#38f9d7"]; // green-cyan

            return (
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.3, delay: idx * 0.1 }}
                key={idx}
                className="group relative w-full cursor-pointer overflow-hidden rounded-2xl p-6 py-8 before:bg-linear-to-b bg-white/80 before:from-white/5 before:to-white/20 before:backdrop-blur-3xl after:bg-linear-to-b after:from-transparent after:via-transparent after:to-white/20 dark:before:bg-linear-to-b dark:bg-black/5 dark:before:from-black/5 dark:before:to-black/20 dark:after:to-black/20 before:absolute before:inset-0 before:rounded-[inherit] before:content-[''] after:absolute after:inset-0 after:rounded-[inherit] after:content-[''] hover:before:bg-primary/5 shadow-[0px_3px_8px_rgba(0,0,0,0.04),0px_12px_20px_rgba(0,0,0,0.08)] hover:shadow-[0px_5px_15px_rgba(0,0,0,0.03),0px_25px_35px_rgba(0,0,0,0.2)] dark:shadow-[0px_3px_8px_rgba(0,0,0,0.08),0px_12px_20px_rgba(0,0,0,0.15)] dark:hover:shadow-[0px_5px_15px_rgba(0,0,0,0.06),0px_25px_35px_rgba(0,0,0,0.4)]"
              >
                <div className="absolute inset-0 overflow-hidden rounded-[inherit] ">
                  <div
                    className="absolute inset-[-200%] opacity-0 transition-opacity duration-300 group-hover:opacity-100"
                    style={{
                      background:
                        "conic-gradient(from 0deg at 50% 50%, transparent 0deg, transparent 340deg, var(--card-color) 360deg)",
                      animation: "spin 4s linear infinite",
                    }}
                  />
                </div>
                <div className="absolute inset-0 z-20 overflow-hidden rounded-[inherit] opacity-100 transition-all duration-500">
                  <div
                    className="absolute bottom-[55%] left-1/2 aspect-square w-[200%] -translate-x-1/2 rounded-[50%]"
                    style={{
                      background:
                        "conic-gradient(from 205deg, transparent 0deg, rgb(5, 87, 194) 20deg, rgba(5, 89, 194, 0.3) 280deg, transparent 360deg)",
                      filter: "blur(40px)",
                    }}
                  />
                </div>
                <div className="flex items-center space-x-3">
                  <div className="w-16 z-50 relative h-16 rounded-full bg-primary/10 flex items-center justify-center mb-4">
                    <img
                      src={`https://robohash.org/${user.name}?size=64x64`}
                      alt={user.name}
                      className="rounded-full"
                    />
                  </div>
                  <div className="flex-1 z-50 relative flex flex-col justify-center">
                    <div className="text-foreground font-semibold text-lg">
                      {user.name}
                    </div>
                    <div className="text-muted-foreground text-sm mb-2">
                      {user.role}
                    </div>
                  </div>
                </div>
                <div className="flex flex-col items-center z-50 relative gap-4 mt-auto pt-2">
                  <div className="flex justify-between w-full">
                    <span className="text-muted-foreground text-sm">
                      Active Campaigns
                    </span>
                    <div className="text-green-500 font-semibold text-sm">
                      +{user.activeCampaigns}
                    </div>
                  </div>
                  <div className="flex justify-between w-full">
                    <span className="text-muted-foreground text-sm">
                      Total Reach
                    </span>
                    <div className="text-blue-500 font-semibold text-sm">
                      {user.totalReach}
                    </div>
                  </div>
                </div>
                <div className="z-50 relative mt-4 flex items-end justify-center w-full gap-2">
                  {barValues.map((val, i) => (
                    <div
                      key={i}
                      className="w-1 rounded-md transition-all duration-300"
                      style={{
                        height: `${val * 1.5}px`,
                        background: `linear-gradient(to top, ${gradientColor[0]}, ${gradientColor[1]})`,
                        filter: `drop-shadow(0 0 2px ${gradientColor[1]})`,
                      }}
                    />
                  ))}
                </div>
              </motion.div>
            );
          })}
        </div>
      </div>
    </div>
  );
}
