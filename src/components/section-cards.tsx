"use client";

import {
  TrendingDown,
  TrendingUp,
  Rocket,
  Users,
  BarChart2,
  Activity,
} from "lucide-react";
import { motion } from "framer-motion";

export interface SectionCardData {
  title: string;
  desc: string;
  value: string;
  trend: "up" | "down";
  trendValue: string;
  icon?: React.ReactNode;
}

export const defaultSectionCardsData: SectionCardData[] = [
  {
    title: "Active Campaigns",
    desc: "Number of ongoing campaigns",
    value: "24",
    trend: "down",
    trendValue: "-12%",
    icon: <Rocket />,
  },
  {
    title: "Users Engaged Today",
    desc: "Users who interacted today",
    value: "1,234",
    trend: "up",
    trendValue: "+8%",
    icon: <Users />,
  },
  {
    title: "Average Campaign Reach",
    desc: "Average audience per campaign",
    value: "45.2K",
    trend: "up",
    trendValue: "+15%",
    icon: <BarChart2 />,
  },
  {
    title: "Song Usage Trends",
    desc: "Songs used in campaigns",
    value: "892",
    trend: "up",
    trendValue: "+23%",
    icon: <Activity />,
  },
];

export function SectionCards({
  data = defaultSectionCardsData,
}: {
  data?: SectionCardData[];
}) {
  return (
    <div className="*:data-[slot=card]:from-blue-500/10 *:data-[slot=card]:to-card dark:*:data-[slot=card]:bg-card grid grid-cols-1 gap-4 *:data-[slot=card]:bg-gradient-to-t *:data-[slot=card]:shadow-xs sm:grid-cols-2 lg:grid-cols-4 @container/card">
      {data.map((item, idx) => (
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.4, delay: idx * 0.2 }}
          key={idx}
          className="transform-gpu space-y-3 rounded-xl border bg-transparent p-5 [box-shadow:0_-20px_80px_-20px_#2664ff2f_inset] relative"
        >
          <div className="rounded-full p-4 text-primary text-xs absolute top-0 right-0">
            {item.icon}
          </div>
          <h4 className="font-geist text-lg font-bold tracking-tighter">
            {item.title}
          </h4>
          <p className="text-gray-500">{item.desc}</p>
          <div className="flex items-center space-x-2">
            <span className="text-2xl font-bold">{item.value}</span>
            <span
              className={
                item.trend === "up"
                  ? "text-green-600 font-semibold"
                  : "text-red-600 font-semibold"
              }
            >
              {item.trend === "up" ? (
                <TrendingUp className="inline w-4 h-4" />
              ) : (
                <TrendingDown className="inline w-4 h-4" />
              )}
              {item.trendValue}
            </span>
          </div>
        </motion.div>
      ))}
    </div>
  );
}
